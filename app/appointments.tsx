import React, { useState } from "react";
import { AppointmentHeader } from "@/components/screens/fitness-consultation/header";
import { Box } from "@/components/ui/box";

import { Text } from "@/components/ui/text";
import { ChevronDownIcon, MapPinIcon } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { AppointmentCard } from "@/components/screens/fitness-consultation/personal-training-card";
import { VStack } from "@/components/ui/vstack";
import { FlatList, Pressable, RefreshControl } from "react-native";
import { useAppointmentByCategory } from "@/data/screens/appointments/queries/useAppointmentByCategory";
import { useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { SearchInput } from "@/components/shared/search";
import { matchSorter } from "match-sorter";

const Appointments = () => {
  const { id, categoryName } = useLocalSearchParams<{
    id: string;
    categoryName: string;
  }>();

  const { data, isPending, refetch, isRefetching } =
    useAppointmentByCategory(id);

  const [searchTerm, setSearchTerm] = useState("");

  const filteredData = searchTerm
    ? matchSorter(data ?? [], searchTerm, {
        keys: ["name", "gym_name", "room_name"],
      })
    : data;

  if (isPending) {
    return (
      <Box className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Text className="text-lg font-dm-sans-medium text-typography-600">
            Loading appointments...
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <Box className="flex-1 bg-gray-50">
        <AppointmentHeader categoryName={categoryName} />

        <VStack space="xs">
          <SearchInput onSearch={setSearchTerm} searchTerm={searchTerm} />
        </VStack>

        <Pressable className="flex-row bg-white items-center gap-2 py-3 px-5 mb-2">
          <Icon as={MapPinIcon} className="text-typography-500" size="sm" />
          <Text className="text-typography-700 flex-1 font-medium">
            Collins & catz family
          </Text>
          <Icon
            as={ChevronDownIcon}
            className="text-typography-500"
            size="sm"
          />
        </Pressable>

        {searchTerm && filteredData?.length === 0 && (
          <Box className="flex-1 bg-white">
            <VStack className="flex-1 justify-center items-center px-4">
              <Text className="text-lg font-dm-sans-medium text-typography-600">
                No results found
              </Text>
            </VStack>
          </Box>
        )}

        <VStack className="pt-4">
          <FlatList
            data={filteredData}
            renderItem={({ item }) => <AppointmentCard {...item} />}
            keyExtractor={(item) => String(item.id)}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={isRefetching}
                onRefresh={refetch}
                tintColor="#00697B"
                colors={["#00697B"]}
              />
            }
            contentContainerStyle={{
              paddingBottom: 400,
            }}
          />
        </VStack>
      </Box>
    </SafeAreaView>
  );
};

export default Appointments;
